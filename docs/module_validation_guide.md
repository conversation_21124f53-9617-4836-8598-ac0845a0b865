# DICOM Module Validation Guide

## Overview

This guide provides comprehensive documentation for implementing and maintaining DICOM module validation in the PyRT-DICOM library. The validation system is designed to validate DICOM datasets whether they were created using PyRT-DICOM or external systems, ensuring compliance with DICOM PS3.3 specifications.

**Scope**: This guide focuses exclusively on **module-level validation**. IOD (Information Object Definition) validation will be covered in a separate guide.

## Architecture Overview

### Validation Infrastructure

The validation system consists of three main components:

1. **BaseValidator** (`src/pyrt_dicom/validators/base_validator.py`)
   - Provides utility methods for common validation patterns
   - Handles enumerated value validation, conditional requirements, and either/or logic
   - Ensures consistent error message formatting

2. **ValidationConfig** (`src/pyrt_dicom/validators/base_validator.py`)
   - Configures validation behavior (strict mode, enumerated values, sequences, conditionals)
   - Allows flexible validation based on use case requirements

3. **Module-Specific Validators** (e.g., `PatientValidator`)
   - Implement DICOM PS3.3 requirements for specific modules
   - Use BaseValidator utilities for consistency
   - Return structured error and warning reports

### Design Principles

- **External Dataset Support**: Validators must work with any pydicom Dataset, not just PyRT-DICOM created ones
- **Structured Results**: All validators return `{"errors": [], "warnings": []}` dictionaries
- **Configurable Validation**: Use ValidationConfig to enable/disable validation categories
- **Clear Error Messages**: Include DICOM tag numbers and descriptive explanations
- **Modular Design**: Each module has its own validator class

## Current Implementation Status

### ✅ Implemented Modules with Validators

| Module | Validator | Status | Notes |
|--------|-----------|--------|-------|
| PatientModule | PatientValidator | Complete | Full PS3.3 C.7.1.1 implementation |
| ClinicalTrialSubjectModule | ClinicalTrialSubjectValidator | Complete | PS3.3 C.7.1.3 implementation |
| PatientStudyModule | PatientStudyValidator | Complete | PS3.3 C.7.2.2 implementation |
| GeneralStudyModule | GeneralStudyValidator | Complete | PS3.3 C.7.2.1 implementation |
| ClinicalTrialStudyModule | ClinicalTrialStudyValidator | Complete | PS3.3 C.7.2.3 implementation |
| GeneralSeriesModule | GeneralSeriesValidator | Complete | PS3.3 C.7.3.1 implementation |
| RTSeriesModule | RTSeriesValidator | Complete | PS3.3 C.8.8.1 implementation |
| ClinicalTrialSeriesModule | ClinicalTrialSeriesValidator | Complete | PS3.3 C.7.3.2 implementation |
| CTImageModule | CTImageValidator | Complete | PS3.3 C.8.2.1 implementation with conditional validation |
| MultiEnergyCTImageModule | MultiEnergyCTImageValidator | Complete | PS3.3 C.8.2.2 implementation with nested sequences |
| ContrastBolusModule | ContrastBolusValidator | Complete | PS3.3 C.7.6.4 implementation with flow validation |
| GeneralAcquisitionModule | GeneralAcquisitionValidator | Complete | PS3.3 C.7.10.1 implementation with timing validation |

### ❌ Missing Modules Requiring Implementation

#### Phase 1 (POC) - Critical Missing Modules
| Module | DICOM Reference | Priority | Notes |
|--------|-----------------|----------|-------|
| GeneralEquipmentModule | PS3.3 C.7.5.1 | Critical | Referenced in examples but missing |
| FrameOfReferenceModule | PS3.3 C.7.4.1 | Critical | Referenced in examples but missing |
| RTDoseModule | PS3.3 C.8.8.3 | Critical | Referenced in examples but missing |
| SOPCommonModule | PS3.3 C.12.1 | Critical | Referenced in examples but missing |
| RTGeneralPlanModule | PS3.3 C.8.8.16 | High | Planned for Phase 1 |

#### Phase 2 - Universal Modules
| Module | DICOM Reference | Priority |
|--------|-----------------|----------|
| CommonInstanceReferenceModule | PS3.3 C.12.2 | Medium |
| GeneralReferenceModule | PS3.3 C.12.3 | Medium |

#### Phase 2 - Image-Related Modules
| Module | DICOM Reference | Priority |
|--------|-----------------|----------|
| GeneralImageModule | PS3.3 C.7.6.1 | High |
| ImagePlaneModule | PS3.3 C.7.6.2 | High |
| ImagePixelModule | PS3.3 C.7.6.3 | High |
| MultiFrameModule | PS3.3 C.7.6.6 | Medium |
| ~~ContrastBolusModule~~ | ~~PS3.3 C.7.6.4~~ | ~~Medium~~ | ✅ Complete |
| DeviceModule | PS3.3 C.7.6.12 | Low |
| VOILUTModule | PS3.3 C.11.2 | Low |
| ModalityLUTModule | PS3.3 C.11.1 | Low |

#### Phase 2 - CT-Specific Modules
| Module | DICOM Reference | Priority |
|--------|-----------------|----------|
| ~~GeneralAcquisitionModule~~ | ~~PS3.3 C.7.10.1~~ | ~~High~~ | ✅ Complete |
| ~~CTImageModule~~ | ~~PS3.3 C.8.2.1~~ | ~~High~~ | ✅ Complete |
| ~~MultiEnergyCTImageModule~~ | ~~PS3.3 C.8.2.2~~ | ~~Low~~ | ✅ Complete |
| EnhancedPatientOrientationModule | PS3.3 C.7.6.17 | Low |
| SpecimenModule | PS3.3 C.7.1.2 | Low |
| OverlayPlaneModule | PS3.3 C.9.2 | Low |
| SynchronizationModule | PS3.3 C.7.6.16 | Low |

#### Phase 2 - RT-Specific Modules
| Module | DICOM Reference | Priority |
|--------|-----------------|----------|
| RTDVHModule | PS3.3 C.8.8.4 | High |
| RTImageModule | PS3.3 C.8.8.2 | High |
| RTToleranceTablesModule | PS3.3 C.8.8.14 | Medium |
| RTPatientSetupModule | PS3.3 C.8.8.12 | Medium |
| RTFractionSchemeModule | PS3.3 C.8.8.13 | Medium |
| RTBeamsModule | PS3.3 C.8.8.14 | Medium |
| RTBrachyApplicationSetupsModule | PS3.3 C.8.8.11 | Medium |
| StructureSetModule | PS3.3 C.8.8.5 | Medium |
| ROIContourModule | PS3.3 C.8.8.6 | Medium |
| RTROIObservationsModule | PS3.3 C.8.8.8 | Medium |
| RTPrescriptionModule | PS3.3 C.8.8.17 | Low |
| ApprovalModule | PS3.3 C.8.8.15 | Low |

#### Phase 2 - Additional Modules
| Module | DICOM Reference | Priority |
|--------|-----------------|----------|
| CineModule | PS3.3 C.7.6.15 | Low |
| FrameExtractionModule | PS3.3 C.7.6.18 | Low |

## Validation Patterns and Best Practices

### Basic Validator Structure

All module validators should follow this pattern:

```python
"""Module Name DICOM validation - PS3.3 Reference"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class ModuleNameValidator:
    """Validator for DICOM Module Name (PS3.3 Reference)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Module Name requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            result = ModuleNameValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = ModuleNameValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = ModuleNameValidator._validate_sequence_requirements(dataset, result)
        
        return result
```

### Validation Categories

#### 1. Type 1C Conditional Requirements

Use `BaseValidator.validate_conditional_requirement()` for Type 1C fields:

```python
@staticmethod
def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
    """Validate Type 1C and 2C conditional requirements."""
    
    # Example: Field required when condition is met
    condition = hasattr(dataset, 'TriggerField')
    BaseValidator.validate_conditional_requirement(
        condition=condition,
        required_fields=['RequiredField'],
        dataset=dataset,
        error_message="Required Field (0008,0001) is required when Trigger Field is present",
        result=result
    )
    
    return result
```

#### 2. Either/Or Requirements

Use `BaseValidator.validate_either_or_requirement()` for alternative requirements:

```python
# Either field A or field B must be present
BaseValidator.validate_either_or_requirement(
    condition=True,  # Always check this requirement
    field_a='FieldA',
    field_b='FieldB',
    dataset=dataset,
    error_message="Either Field A (0008,0001) or Field B (0008,0002) is required",
    result=result
)
```

#### 3. Enumerated Value Validation

Use `BaseValidator.validate_enumerated_value()` for controlled vocabularies:

```python
@staticmethod
def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
    """Validate enumerated values against DICOM specifications."""
    
    field_value = getattr(dataset, 'EnumeratedField', '')
    if field_value:
        BaseValidator.validate_enumerated_value(
            field_value, ["VALUE1", "VALUE2", "VALUE3"],
            "Enumerated Field (0008,0001)", result
        )
    
    return result
```

#### 4. Sequence Validation

Validate sequence structures and required items:

```python
@staticmethod
def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
    """Validate sequence structure requirements."""
    
    sequence = getattr(dataset, 'ExampleSequence', [])
    for i, item in enumerate(sequence):
        if not item.get('RequiredAttribute'):
            result["errors"].append(
                f"Example Sequence item {i}: Required Attribute (0008,0001) is required"
            )
    
    return result
```

### Error Message Guidelines

- Include DICOM tag numbers in parentheses: `(0008,0001)`
- Use descriptive field names: `"Patient's Sex (0010,0040)"`
- Provide context for conditional requirements
- Use consistent formatting across all validators

## Implementation Templates

### Simple Module Validator Template

For modules with minimal validation requirements:

```python
"""Simple Module DICOM validation - PS3.3 Reference"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class SimpleModuleValidator:
    """Validator for DICOM Simple Module (PS3.3 Reference)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Simple Module requirements on any pydicom Dataset."""
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Check Type 1 requirements
        if not hasattr(dataset, 'RequiredField') or not dataset.RequiredField:
            result["errors"].append("Required Field (0008,0001) is required (Type 1)")
        
        # Check enumerated values if configured
        if config.check_enumerated_values:
            field_value = getattr(dataset, 'EnumeratedField', '')
            if field_value:
                BaseValidator.validate_enumerated_value(
                    field_value, ["VALID1", "VALID2"],
                    "Enumerated Field (0008,0002)", result
                )
        
        return result
```

### Complex Module Validator Template

For modules with extensive validation requirements (like PatientModule):

```python
"""Complex Module DICOM validation - PS3.3 Reference"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class ComplexModuleValidator:
    """Validator for DICOM Complex Module (PS3.3 Reference)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Complex Module requirements on any pydicom Dataset."""
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Validate conditional requirements
        if config.validate_conditional_requirements:
            result = ComplexModuleValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            result = ComplexModuleValidator._validate_enumerated_values(dataset, result)
        
        # Validate sequence structures
        if config.validate_sequences:
            result = ComplexModuleValidator._validate_sequence_requirements(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate Type 1C and 2C conditional requirements."""
        # Implementation specific to module requirements
        return result
    
    @staticmethod
    def _validate_enumerated_values(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate enumerated values against DICOM specifications."""
        # Implementation specific to module requirements
        return result
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: dict[str, list[str]]) -> dict[str, list[str]]:
        """Validate sequence structure requirements."""
        # Implementation specific to module requirements
        return result
```

## Placeholder Validator Implementations

### Critical Missing Module Validators

The following placeholder validators should be implemented immediately for the missing critical modules:

#### GeneralEquipmentValidator

```python
"""General Equipment Module DICOM validation - PS3.3 C.7.5.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class GeneralEquipmentValidator:
    """Validator for DICOM General Equipment Module (PS3.3 C.7.5.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate General Equipment Module requirements on any pydicom Dataset."""
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Type 2 - Manufacturer can be empty but should be present
        if not hasattr(dataset, 'Manufacturer'):
            result["warnings"].append("Manufacturer (0008,0070) is recommended (Type 2)")
        
        # Add warnings for other recommended fields
        recommended_fields = [
            ('InstitutionName', '0008,0080'),
            ('InstitutionAddress', '0008,0081'),
            ('StationName', '0008,1010'),
            ('ManufacturerModelName', '0008,1090'),
            ('SoftwareVersions', '0018,1020')
        ]
        
        for field_name, tag in recommended_fields:
            if not hasattr(dataset, field_name):
                result["warnings"].append(f"{field_name} ({tag}) is recommended for equipment identification")
        
        return result
```

#### FrameOfReferenceValidator

```python
"""Frame of Reference Module DICOM validation - PS3.3 C.7.4.1"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig


class FrameOfReferenceValidator:
    """Validator for DICOM Frame of Reference Module (PS3.3 C.7.4.1)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate Frame of Reference Module requirements on any pydicom Dataset."""
        config = config or ValidationConfig()
        result = {"errors": [], "warnings": []}
        
        # Type 1 - Frame of Reference UID is required
        if not hasattr(dataset, 'FrameOfReferenceUID') or not dataset.FrameOfReferenceUID:
            result["errors"].append("Frame of Reference UID (0020,0052) is required (Type 1)")
        
        # Type 2 - Position Reference Indicator can be empty
        if not hasattr(dataset, 'PositionReferenceIndicator'):
            result["warnings"].append("Position Reference Indicator (0020,1040) is recommended (Type 2)")
        
        return result
```

## Testing and Quality Assurance

### Testing Strategy

1. **Unit Tests**: Test each validator with known valid and invalid datasets
2. **Integration Tests**: Test validators with real DICOM files from external systems
3. **Edge Case Tests**: Test boundary conditions and unusual but valid combinations
4. **Performance Tests**: Ensure validators perform efficiently on large datasets

### Test Data Sources

- Use pydicom test datasets for baseline testing
- Include real-world DICOM files from various vendors
- Create synthetic test cases for edge conditions
- Test with both PyRT-DICOM generated and external datasets

### Validation Test Template

```python
import pytest
from pydicom import Dataset
from pyrt_dicom.validators.module_validator import ModuleValidator
from pyrt_dicom.validators.base_validator import ValidationConfig


class TestModuleValidator:
    def test_valid_module(self):
        """Test validation of valid module data."""
        ds = Dataset()
        ds.RequiredField = "ValidValue"
        
        result = ModuleValidator.validate(ds)
        assert result["errors"] == []
    
    def test_missing_required_field(self):
        """Test validation fails when required field is missing."""
        ds = Dataset()
        
        result = ModuleValidator.validate(ds)
        assert len(result["errors"]) > 0
        assert "Required Field" in result["errors"][0]
    
    def test_invalid_enumerated_value(self):
        """Test validation warns about invalid enumerated values."""
        ds = Dataset()
        ds.RequiredField = "ValidValue"
        ds.EnumeratedField = "InvalidValue"
        
        result = ModuleValidator.validate(ds)
        assert len(result["warnings"]) > 0
        assert "InvalidValue" in result["warnings"][0]
    
    def test_validation_config_options(self):
        """Test that validation config options work correctly."""
        ds = Dataset()
        ds.RequiredField = "ValidValue"
        ds.EnumeratedField = "InvalidValue"
        
        # Disable enumerated value checking
        config = ValidationConfig(check_enumerated_values=False)
        result = ModuleValidator.validate(ds, config)
        assert result["warnings"] == []
```

## Next Steps

1. **Critical Missing**: Create source files and validators for GeneralEquipmentModule, FrameOfReferenceModule, RTDoseModule, SOPCommonModule
2. **Phase 2 Planning**: Prioritize image-related and RT-specific module validators based on IOD implementation needs
3. **Testing Infrastructure**: Establish comprehensive test suite for all validators
4. **Documentation**: Create module-specific validation documentation as validators are implemented

## Conclusion

This validation guide provides the foundation for implementing comprehensive DICOM module validation across the PyRT-DICOM library. By following these patterns and best practices, developers can ensure consistent, reliable validation that supports both PyRT-DICOM generated datasets and external DICOM files.

Remember: **Focus on module validation only**. IOD validation will be addressed in a separate guide that builds upon this module-level foundation.
